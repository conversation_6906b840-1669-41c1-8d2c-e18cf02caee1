{"name": "chat-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chat", "ai", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "groq-sdk": "^0.23.0", "openai": "^5.1.0"}, "description": "Chat AI Backend with Express.js", "engines": {"node": ">=18.0.0"}}