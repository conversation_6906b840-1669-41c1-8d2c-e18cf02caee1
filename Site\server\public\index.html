<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <title>Pan.IA - Assistente Inteligente</title>

  <!-- Favicon opcional (adicione o arquivo pania-icon.png na mesma pasta se quiser) -->
  <!-- <link rel="icon" href="pania-icon.png" /> -->

  <!-- PrismJS para destaque de código -->
  <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet" />

  <!-- Seu CSS externo -->
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <h1>🤖 Pan.IA</h1>
  <p style="text-align: center; color: #888; font-style: italic; margin-top: -10px; margin-bottom: 20px;">
    Olá! Eu sou a <strong>Pan.IA</strong>, sua assistente virtual ✨
  </p>

  <div id="chat"></div>

  <form onsubmit="event.preventDefault(); enviarPergunta();">
    <input type="text" id="inputPergunta" placeholder="Digite sua pergunta..." autocomplete="off" />
    <button id="btnEnviar">Enviar</button>
  </form>

  <!-- Scripts: PrismJS e Marked.js -->
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

  <!-- Script principal com animação -->
  <script>
    const chatDiv = document.getElementById('chat');
    const inputPergunta = document.getElementById('inputPergunta');
    const btnEnviar = document.getElementById('btnEnviar');

    function adicionarMensagemHTML(html, classe) {
      const div = document.createElement('div');
      div.className = 'mensagem ' + classe;
      div.innerHTML = html;
      chatDiv.appendChild(div);
      Prism.highlightAll();
      chatDiv.scrollTop = chatDiv.scrollHeight;
      return div;
    }

    function simularDigitacao(textoMarkdown, elemento, velocidade = 15) {
      return new Promise((resolve) => {
        const htmlCompleto = marked.parse(textoMarkdown);
        const container = document.createElement('div');
        let i = 0;

        function escrever() {
          if (i <= htmlCompleto.length) {
            container.innerHTML = htmlCompleto.slice(0, i);
            elemento.innerHTML = container.innerHTML;
            Prism.highlightAll();
            chatDiv.scrollTop = chatDiv.scrollHeight;
            i++;
            setTimeout(escrever, velocidade);
          } else {
            resolve();
          }
        }

        escrever();
      });
    }

    async function enviarPergunta() {
      const pergunta = inputPergunta.value.trim();
      if (!pergunta) return;

      adicionarMensagemHTML(marked.parse('**Você:** ' + pergunta), 'user');
      inputPergunta.value = '';
      inputPergunta.disabled = true;
      btnEnviar.disabled = true;

      // Adiciona "pensando..."
      const pensandoDiv = adicionarMensagemHTML('<em>Pan.IA está pensando...</em>', 'bot');

      try {
        const resposta = await fetch('/api/perguntar', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ pergunta })
        });

        const data = await resposta.json();

        // Simula tempo de "pensar"
        await new Promise(res => setTimeout(res, 1000));

        // Substitui por digitação animada
        pensandoDiv.innerHTML = '';
        await simularDigitacao('**Pan.IA:**\n\n' + (data.resposta || 'Sem resposta.'), pensandoDiv);
      } catch (e) {
        pensandoDiv.innerHTML = 'Erro ao se comunicar com o servidor.';
      }

      inputPergunta.disabled = false;
      btnEnviar.disabled = false;
      inputPergunta.focus();
    }

    btnEnviar.addEventListener('click', enviarPergunta);
    inputPergunta.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') enviarPergunta();
    });
  </script>
</body>
</html>
